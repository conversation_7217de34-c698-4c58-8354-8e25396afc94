import { success, warn } from "iuap-gp-ymscloudui-fe/components";
import {
  Button,
  Form,
  Icon
} from 'iuap-gp-ymscloudui-fe/baseui';
import { transferPipeline } from 'newDevopsService/NewService';
import React, { Component } from "react";

const FormItem = Form.Item;

import "./LineTrans.less";
import UserModal from "../../../../components/UserModal";

class LineTrans extends Component {
  state = {
    /* 告知人或者审批人 */
    userList: [],
    examineType: '1', // 默认选中审核
    showData: [],
    showUserModal: false
  };
  flag = false;

  // 重构数组
  rebuildApprover = (value, type) => {
    let dataArr = []
    value.map((item) => {
      dataArr.push(item[type])
    })
    return dataArr
  }


  handleModal = (type) => () => {
    this.setState({
      showUserModal: type
    })
  }

  handleAdd = (list) => {
    let { userList } = this.state;
    if (userList.find(it => it.userId == list[0].userId)) {
      return warn(lang.templateByUuid("UID:P_GPAASDEVOPS-FE_17EC411004C81767", "该审批人已添加！") /* "该审批人已添加！" */);
    } else {
      this.setState({
        userList: userList.concat(list)
      })
    }
    this.flag = true
  }

  sureTrans = async () => {
    let { userList } = this.state
    let { baseData, getMainById, onClose } = this.props

    if (userList.length == 0) {
      return warn(lang.templateByUuid("UID:P_GPAASDEVOPS-FE_17EC411004C81766", "当前没有转移目标，请添加转移目标！") /* "当前没有转移目标，请添加转移目标！" */)
    }
    let params = {
      fromUserId: baseData.userId,
      fromUserName: baseData.userName,
      toUserId: userList[0].userId,
      toUserName: userList[0].userName,
      appCode: baseData.pipelineCode
    }
    let result = await transferPipeline(params)
    success(result || lang.templateByUuid("UID:P_GPAASDEVOPS-FE_17EC411004C8176A", "转移成功") /* "转移成功" */)
    getMainById(baseData.devopsPipelineMainId)
    onClose()
  }

  delUser = (userid) => () => {
    let newList = this.state.userList.filter(it => it.userId != userid)
    if (newList.length == 0) {
      this.flag = false
    }
    this.setState({
      userList: newList
    })
  }

  render() {
    let { userList, showData } = this.state;
    let { baseData } = this.props
    return (
      <div className="line_trans">
        <Form
          // {...layout}
          layout="inline"
        >
          <FormItem label={lang.templateByUuid("UID:P_GPAASDEVOPS-FE_17EC411004C81769", "流水线创建人:") /* "流水线创建人:" */} style={{ width: 300 }}>
            <span>{baseData.userName}</span>
          </FormItem>
          <FormItem label={lang.templateByUuid("UID:P_GPAASDEVOPS-FE_17EC411004C81764", "转移目标:") /* "转移目标:" */} style={{ width: 300 }}>

            <div className="user-list">
              {
                userList && userList.length ? userList.map((item) => (
                  <span className="user-item" key={item.id}>
                    {item.name}
                    {
                      <Icon fieldid="newFlow_PnWyhRYf7z_expansion" onClick={this.delUser(item.userId)} type="uf-close-c" />
                    }
                  </span>
                )) : (
                  <Button fieldid="newFlow_lotwfKhb2k_btn" onClick={this.handleModal(true)}>{lang.templateByUuid("UID:P_GPAASDEVOPS-FE_17EC411004C81768", "选择目标转移") /* "选择目标转移" */}</Button>
                )
              }
            </div>
          </FormItem>
          <FormItem >
            <Button fieldid="newFlow_b9s8x246CL_btn" onClick={this.sureTrans}>{lang.templateByUuid("UID:P_GPAASDEVOPS-FE_17EC411004C81765", "转移") /* "转移" */}</Button>
          </FormItem>
        </Form>

        <UserModal
          onClose={this.handleModal(false)}
          onEnsure={this.handleAdd}
          show={this.state.showUserModal}
          userData={showData}
        />
      </div>
    );
  }
}

export default Form.createForm()(LineTrans);
