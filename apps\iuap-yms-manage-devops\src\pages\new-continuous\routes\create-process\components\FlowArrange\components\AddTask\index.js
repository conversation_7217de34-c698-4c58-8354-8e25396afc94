import { isPrivate } from '@/constants';
import { ASSEM_ITEM_SIMPLE } from '@/constants/newContinuous';
import TooltipHelp from '@/pages/new-continuous/components/TooltipHelp';
import { warn } from 'iuap-gp-ymscloudui-fe/components';
import { Button, Drawer } from 'iuap-gp-ymscloudui-fe/baseui';
import classNames from 'classnames';
import { toJS } from 'mobx';
import { inject, observer } from 'mobx-react';
import React, { useState, useRef, useImperativeHandle, forwardRef } from 'react';
import TaskComps from '../../../ArrangeNodes';
import './index.less';
import NodeGroup from '../NodeGroup';

const AddTask = forwardRef((props, ref) => {
  const refObj = useRef({}).current;
  const [renderkey, setRenderkey] = useState(0);

  useImperativeHandle(ref, () => ({
    refObj,
    renderkey
  }));

  // 选择节点
  const selectWorkGroups = (e, item) => {
    e.stopPropagation();
    let { CreateProcessStore } = props;
    let { handleCurrentNode } = CreateProcessStore;

    if (toJS(item).id == 'publish') {
      // 发布节点默认选中二方包发布选项
      handleCurrentNode({ taskCode: 'mavenDeploy' })
    } else if (toJS(item).id == 'database') {
      // 数据库节点默认选中数据库规范检查
      handleCurrentNode({ taskCode: 'sql' })
    } else if (toJS(item).id == 'deploy') {
      // 部署节点默认选中部署
      handleCurrentNode({ taskCode: 'executionDeploy' })
    } else if (toJS(item).id == 'nginxManager') {
      // 前端引擎默认选中CI
      handleCurrentNode({ taskCode: 'nginxManagerCI' })
    } else {
      handleCurrentNode({ taskCode: toJS(item).id, })
    }
  }

  // 是否允许插入节点
  const getAllowAppend = nodes => {
    const { CreateProcessStore } = props
    const { currentNodeInfo } = CreateProcessStore
    const { indexId, nodeIndex } = toJS(currentNodeInfo)
    const nodesIndex = getNodesIndex(nodes)
    let allowAppend = true, i = 0
    while (allowAppend && i < nodes.length) {
      if (nodesIndex[nodes[i]] === undefined) {
        allowAppend = false
        continue
      }
      if (nodeIndex === 0) {
        allowAppend = nodesIndex[nodes[i]] <= indexId // 最后插入新stage
      } else {
        allowAppend = nodesIndex[nodes[i]] < indexId // 已有stage增加node
      }
      i++
    }
    return allowAppend
  }

  // 获取当前模版中节点index
  const getNodesIndex = nodes => {
    const { CreateProcessStore } = props
    const { pipelineStages } = CreateProcessStore
    let nodesIndex = {}
    let stages = toJS(pipelineStages)
    stages.map((stage, index) => {
      stage.taskList.map(task => {
        if (nodes.includes(task.code)) {
          nodesIndex[task.code] = index
        }
      })
    })
    return nodesIndex
  }

  // 添加节点
  const addNode = () => {
    let {
      addPipelineStages,
      currentNodeInfo,
      handleCurrentNode,
      baseData,
      pipelineStages
    } = props.CreateProcessStore;

    let sourceCodeType = pipelineStages[0].taskList[0].code;
    let { taskCode, indexId, type, addSourceNode, pluginName } = currentNodeInfo;
    taskCode = taskCode === 'customPluginParam' ? 'plugin' : taskCode;
    taskCode = taskCode || (addSourceNode ? 'gitCode' : 'compileCode')
    let node = refObj[currentNodeInfo.taskCode]

    const findObj = TaskComps.find(it => it.id == taskCode);

    const canAdd = findObj.beforeAdd ? findObj.beforeAdd({ currentNodeInfo, baseData, pipelineStages, node }) : true;
    if (!canAdd) {
      return
    }

    let devopsType = baseData?.label?.devopsType
    if ((taskCode == 'mavenDeploy') && ['yms', 'j2ee', 'j2se'].indexOf(devopsType) == -1) {
      return warn(lang.templateByUuid("UID:P_GPAASDEVOPS-FE_17EC411004C81091", "python，go，php，node，静态网站等应用类型不可以开启该节点") /* "python，go，php，node，静态网站等应用类型不可以开启该节点" */)
    }
    if ((taskCode == 'dependencyCheck') && ['yms', 'j2ee', 'j2se', 'nginx', 'nodejs', 'customType'].indexOf(devopsType) == -1) {
      return warn(lang.templateByUuid("UID:P_GPAASDEVOPS-FE_18F928C40508011B", "python，go，php等应用类型不可以开启该节点") /* "python，go，php等应用类型不可以开启该节点" */)
    }
    if (taskCode == 'ymsWarBackfill') {
      if (devopsType != "yms") {
        return warn(lang.templateByUuid("UID:P_GPAASDEVOPS-FE_17EC411004C81085", "仅YMS应用可以开启此节点") /* "仅YMS应用可以开启此节点" */)
      } else if (sourceCodeType != "uploadApp") {
        return warn(lang.templateByUuid("UID:P_GPAASDEVOPS-FE_17EC411004C81086", "仅当流水线源为「上传应用包」时才能开启「YmsWar回填」") /* "仅当流水线源为「上传应用包」时才能开启「YmsWar回填」" */)
      }
    }
    if (props.envCode != "daily-dedicated" && !isPrivate() && taskCode == 'sql') {
      return warn(lang.templateByUuid("UID:P_GPAASDEVOPS-FE_17EC411004C81084", "多数据适配节点只能在日常多数据库环境下开启") /* "多数据适配节点只能在日常多数据库环境下开启" */)
    }

    let param = {
      showModal: true,
      type: "edit",
      taskCode: taskCode,
      indexId: type == "serial" ? (indexId + 1) : indexId,
      isNew: true
    }
    if (["executionDeploy", "multiDeploy", "diversionDeploy", "grayDeploy"].includes(taskCode)) {
      param.isNew = true // 由于新增时的type也是edit，无法区分是编辑还是新增，改type范围较大，先新增一个参数isNew
    }
    let obj = TaskComps.find(it => it.id == taskCode)
    const newObj = { ...obj };
    if (taskCode === 'plugin') {
      newObj.pluginName = pluginName;
    }
    addPipelineStages(newObj)
    handleCurrentNode({ ...param })
    node && node.saveData && node.saveData()
  }

  // 删除节点
  const delNode = () => {
    let { currentNodeInfo, delPipelineStages, handleCurrentNode } = props.CreateProcessStore;
    let { indexId, nodeIndex, taskCode } = currentNodeInfo;
    if (taskCode === "diversionDeploy") {
      warn(lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1FECD2B0052800D8", "该操作不会销毁当前流量组标识对应的分流实例，如需销毁，请到微服务详情-流量组管理中进行销毁") /* "该操作不会销毁当前流量组标识对应的分流实例，如需销毁，请到微服务详情-流量组管理中进行销毁" */)
    }
    delPipelineStages('node')(indexId, nodeIndex)
    handleCurrentNode({ showModal: false, isNew: false })
  }

  // 保存节点信息
  const handleSaveNodeInfo = async () => {
    const { currentNodeInfo, handleCurrentNode } = props.CreateProcessStore;
    let { addSourceNode, taskCode } = currentNodeInfo
    taskCode = taskCode === 'customPluginParam' ? 'plugin' : taskCode;
    let node = refObj[taskCode || (addSourceNode ? 'gitCode' : 'compileCode')]
    if (node && node.saveData) {
      await node.saveData()
    }
    console.log("node && node.flag", node, node.flag, taskCode, toJS(currentNodeInfo));
    if (node && node.flag) {
      handleCurrentNode({ showModal: false, isNew: false })
    }
  }

  // 关闭抽屉
  const onClose = () => {
    let { CreateProcessStore } = props;
    let { handleCurrentNode } = CreateProcessStore;
    handleCurrentNode({ showModal: false, isNew: false })
  }

  const getRefNode = (code, ref) => {
    if (code) {
      refObj[code] = ref
    }
  }

  let { show, CreateProcessStore, executePublicScriptDisabled } = props;
  let {
    pipelineTaskList,
    currentNodeInfo,
    pipelineStages,
    baseData
  } = CreateProcessStore;
  let devopsType = baseData?.label?.devopsType;
  let { taskCode, type, taskinfo, showModal, addSourceNode } = currentNodeInfo;
  taskCode = taskCode === 'customPluginParam' ? 'plugin' : taskCode;
  taskCode = taskCode || (addSourceNode ? 'gitCode' : 'compileCode');
  const isEdit = type != 'edit';
  if (['plugin'].indexOf(taskCode) != -1 && type != 'edit') {
    taskCode = 'customPlugin'
  }
  if (['executionDeploy', 'multiDeploy', 'diversionDeploy', 'grayDeploy'].indexOf(taskCode) != -1 && type != 'edit') {
    taskCode = 'deploy'
  }
  if (['nginxManagerCI', 'nginxManagerCD', 'nginxManagerDiversionCD'].indexOf(taskCode) != -1 && type != 'edit') {
    taskCode = 'nginxManager'
  }
  let findObj = TaskComps.find(it => it.id == taskCode);
  if (type !== 'edit' && findObj.parent) {
    taskCode = findObj.parent;
    findObj = TaskComps.find(it => it.id == taskCode);
  }
  let TaskComp = findObj && findObj.component;
  let OBJ = {
    'serial': lang.templateByUuid("UID:P_GPAASDEVOPS-FE_17EC411004C8108E", "添加：阶段") /* "添加：阶段" */,
    'edit': lang.templateByUuid("UID:P_GPAASDEVOPS-FE_17EC411004C8108F", "编辑") /* "编辑" */,
    'parallel': lang.templateByUuid("UID:P_GPAASDEVOPS-FE_17EC411004C81090", "添加：任务") /* "添加：任务" */
  }
  let title = <span className='drawer-header'>
    <span>{OBJ[type]}</span>
    {
      isEdit ?
        <Button fieldid="newFlow_cKnjrUPt9d_expansion" type='primary' onClick={addNode} style={{ marginLeft: 20 }}>{lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1A13293A05D80045", "添加") /* "添加" */}</Button>
        :
        (
          (taskCode == 'gitCode' || taskCode == 'useImage' || taskCode == 'uploadApp' || taskCode == 'fromPipeline') && (pipelineStages[0]?.taskList?.length <= 1)
            ? null :
            <Button fieldid="newFlow_Bxa5pNPoet_expansion" type='primary' onClick={delNode} style={{ marginLeft: 20 }}>{lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1A13293A05D80044", "删除") /* "删除" */}</Button>
        )
    }
  </span>
  let footer = isEdit ? null :
    <div className='drawer-foot-btns'>
      <Button fieldid="newFlow_B32LmdCQFP_btn" colors="secondary" style={{ marginRight: "8px" }} onClick={onClose}>{lang.templateByUuid("UID:P_GPAASDEVOPS-FE_17EC411004C81087", "取消") /* "取消" */}</Button>
      <Button fieldid="newFlow_unqpZwc5kE_btn" colors="primary" onClick={handleSaveNodeInfo}>{lang.templateByUuid("UID:P_GPAASDEVOPS-FE_17EC411004C81088", "保存") /* "保存" */}</Button>
    </div>

  let grandChildRefList = ['codeScan', 'makingImage', 'executionDeploy', 'oss', 'examine', 'javaUnitTest', 'sql', 'requestDeploy', 'customShell', 'diversionDeploy', 'grayDeploy', 'plugin']
  let refObjForComponent = {}
  if (grandChildRefList.indexOf(taskCode) != -1) {
    refObjForComponent.wrappedComponentRef = ref => getRefNode(taskCode, ref)
  } else {
    refObjForComponent.ref = ref => getRefNode(taskCode, ref)
  }
  pipelineTaskList = devopsType == "jq" ? ASSEM_ITEM_SIMPLE : pipelineTaskList
  return (
    <div>
      <Drawer fieldid="newFlow_KdIcM6RqbS_Drawer"
        showMask={true}
        className={classNames('add-task-drawer', type == 'edit' && 'add-task-drawer-edit')}
        placement='right'
        title={title}
        maskClosable={true}
        // mask={isEdit}
        show={show}
        getPopupContainer={() => document.querySelector('.create-pipeline-containers-content')}
        onClose={onClose}
        width={"50%"}
        showClose={true}
        footer={footer}
        footerStyle={{ textAlign: "right" }}
      >
        <div className="add-task-drawer-containers">
          {
            isEdit && (!addSourceNode) &&
            <div className="drawer-task-list">
              {
                pipelineTaskList.map((jobItem, index) => {
                  const { code, id, icon, name, show, tip } = jobItem;
                  if (!show) return
                  return (
                    <div
                      className={taskCode == id ? "drawer-task-item active" : 'drawer-task-item'}
                      fieldid={`newFlow_uzjcQtnwVG${index}_expansion`} onClick={(e) => selectWorkGroups(e, jobItem)}
                      key={code}
                    >
                      <div>
                        <img src={require(`@/assets/images/${icon || id}.svg`)} />
                      </div>
                      <div>
                        {name}
                        {tip ? <TooltipHelp overlay={tip} /> : ''}
                      </div>
                    </div>
                  )
                })
              }
            </div>
          }
          {
            taskCode && showModal &&
            <div className={classNames("drawer-task-content", `${taskCode}-drawer-content`)}>
              {
                findObj?.type === 'NodeGroup' ? (
                  <NodeGroup
                    id={findObj.id}
                    nodelist={findObj.nodelist}
                  />
                ) : (
                  <TaskComp
                    isEdit={props.isEdit}
                    taskinfo={taskinfo}
                    {...refObjForComponent}
                    env={props.env}
                    devopsType={baseData?.label?.devopsType}
                    showModal={showModal}
                    controlModal={props.controlModal}
                    id={props.id}
                    appName={props.appName}
                    appCode={props.appCode}
                    envCode={props.envCode}
                    envName={props.envName}
                    showAppConfigModal={props.showAppConfigModal}
                    type={props.type}
                    dcCode={props.dcCode}
                    templateId={props.templateId}
                    applicationCode={props.applicationCode}
                    key={renderkey}
                    renderkey={renderkey}
                    reRender={() => { setRenderkey(renderkey + 1) }}
                    executePublicScriptDisabled={executePublicScriptDisabled}
                  />
                )
              }
            </div>
          }
        </div>
      </Drawer>
    </div>
  )
});

export default inject('CreateProcessStore')(observer(AddTask));
